package modules

import (
	"strings"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"

	"go.uber.org/fx"
)

// GlobalRegistry is the global module registry
var GlobalRegistry = NewModuleRegistry()

// RegisterModule registers a module with global registry
func RegisterModule(module FXModule) error {
	return GlobalRegistry.Register(module)
}

// DiscoverModules discovers and loads modules from configuration
func DiscoverModules(cfg config.Config, log logger.Logger) ([]fx.Option, error) {
	// Debug: List all registered modules
	registeredModules := GlobalRegistry.List()
	log.Info("Registered modules count", "count", len(registeredModules))
	for _, module := range registeredModules {
		log.Info("Registered module", "name", module.Name())
	}

	// Get enabled modules from config
	enabledModulesStr := cfg.GetStringWithDefault("MODULES_ENABLED", "")
	enabledModules := strings.Split(enabledModulesStr, ",")

	// Clean module names
	for i, module := range enabledModules {
		enabledModules[i] = strings.TrimSpace(module)
	}

	// Filter empty strings
	var filteredModules []string
	for _, module := range enabledModules {
		if module != "" {
			filteredModules = append(filteredModules, module)
		}
	}

	// Create loader config
	loaderConfig := LoaderConfig{
		EnabledModules: filteredModules,
		ModuleConfigs:  GetModuleConfigs(cfg),
		Logger:         log,
	}

	// Create loader and load modules
	loader := NewModuleLoader(GlobalRegistry, loaderConfig)
	return loader.LoadModules()
}

// GetModuleConfigs extracts module-specific configurations
func GetModuleConfigs(cfg config.Config) map[string]map[string]interface{} {
	configs := make(map[string]map[string]interface{})

	// List of known modules - in a real implementation, this could be dynamic
	knownModules := []string{"tenant", "auth", "onboarding", "rbac", "notification", "hello", "media", "blog", "product", "seo", "agent-ai"}

	for _, moduleName := range knownModules {
		moduleConfig := cfg.GetModuleSettings(moduleName)
		if len(moduleConfig) > 0 {
			configs[moduleName] = moduleConfig
		}
	}

	return configs
}

// ListAvailableModules returns list of available modules
func ListAvailableModules() []ModuleInfo {
	modules := GlobalRegistry.List()
	var info []ModuleInfo

	for _, module := range modules {
		info = append(info, ModuleInfo{
			Name:         module.Name(),
			Dependencies: module.Dependencies(),
			Priority:     module.Priority(),
			Module:       module.Module(),
		})
	}

	return info
}
